include_configs:
  - ../train/vae/vae_128x128x128_sparse.yaml
  - dataset.yaml

name: 'scene-recon/waymo_wds'

# data
batch_size: 1
accumulate_grad_batches: 16
voxel_size: 0.1
use_fvdb_loader: true
_fvdb_grid_type: 'vs01'
_input_slect_ids: [0]
_input_frame_offsets: [0]
_sup_slect_ids: [0]
_sup_frame_offsets: [0]
grid_crop_bbox_min: [-10.24, -51.2, -12.8]
grid_crop_bbox_max: [92.16, 51.2, 38.4]
grid_crop_augment: true
grid_crop_augment_range: [6.4, 6.4, 1.6]
replace_all_car_with_cad: true # rather than using bounding box, using CAD model makes more sense

# adjust input setting - use semantic and intensity
use_input_normal: false
use_input_semantic: true
use_input_intensity: false
num_semantic: 23
dim_semantic: 32


# adjust supervision - add semantic supervision
kl_weight: 0.3
kl_weight_max: 0.3
supervision:
  semantic_weight: 20.0
  normal_weight: 0

# adjust network
cut_ratio: 16

# consistent coarsen
use_hash_tree: false

network:
  unet:
    params:
      neck_bound: [128, 128, 64]